import cjs from "./sass.node.js";

export const compile = cjs.compile;
export const compileAsync = cjs.compileAsync;
export const compileString = cjs.compileString;
export const compileStringAsync = cjs.compileStringAsync;
export const initCompiler = cjs.initCompiler;
export const initAsyncCompiler = cjs.initAsyncCompiler;
export const Compiler = cjs.Compiler;
export const AsyncCompiler = cjs.AsyncCompiler;
export const Logger = cjs.Logger;
export const SassArgumentList = cjs.SassArgumentList;
export const SassBoolean = cjs.SassBoolean;
export const SassCalculation = cjs.SassCalculation;
export const CalculationOperation = cjs.CalculationOperation;
export const CalculationInterpolation = cjs.CalculationInterpolation;
export const SassColor = cjs.SassColor;
export const SassFunction = cjs.SassFunction;
export const SassList = cjs.SassList;
export const SassMap = cjs.SassMap;
export const SassMixin = cjs.SassMixin;
export const SassNumber = cjs.SassNumber;
export const SassString = cjs.SassString;
export const Value = cjs.Value;
export const CustomFunction = cjs.CustomFunction;
export const ListSeparator = cjs.ListSeparator;
export const sassFalse = cjs.sassFalse;
export const sassNull = cjs.sassNull;
export const sassTrue = cjs.sassTrue;
export const Exception = cjs.Exception;
export const PromiseOr = cjs.PromiseOr;
export const info = cjs.info;
export const render = cjs.render;
export const renderSync = cjs.renderSync;
export const TRUE = cjs.TRUE;
export const FALSE = cjs.FALSE;
export const NULL = cjs.NULL;
export const types = cjs.types;
export const NodePackageImporter = cjs.NodePackageImporter;
export const deprecations = cjs.deprecations;
export const Version = cjs.Version;
export const parser_ = cjs.parser_;

let printedDefaultExportDeprecation = false;
function defaultExportDeprecation() {
  if (printedDefaultExportDeprecation) return;
  printedDefaultExportDeprecation = true;
  console.error(
      "`import sass from 'sass'` is deprecated.\n" +
      "Please use `import * as sass from 'sass'` instead.");
}

export default {
  get compile() {
    defaultExportDeprecation();
    return cjs.compile;
  },
  get compileAsync() {
    defaultExportDeprecation();
    return cjs.compileAsync;
  },
  get compileString() {
    defaultExportDeprecation();
    return cjs.compileString;
  },
  get compileStringAsync() {
    defaultExportDeprecation();
    return cjs.compileStringAsync;
  },
  get initCompiler() {
    defaultExportDeprecation();
    return cjs.initCompiler;
  },
  get initAsyncCompiler() {
    defaultExportDeprecation();
    return cjs.initAsyncCompiler;
  },
  get Compiler() {
    defaultExportDeprecation();
    return cjs.Compiler;
  },
  get AsyncCompiler() {
    defaultExportDeprecation();
    return cjs.AsyncCompiler;
  },
  get Logger() {
    defaultExportDeprecation();
    return cjs.Logger;
  },
  get SassArgumentList() {
    defaultExportDeprecation();
    return cjs.SassArgumentList;
  },
  get SassBoolean() {
    defaultExportDeprecation();
    return cjs.SassBoolean;
  },
  get SassCalculation() {
    defaultExportDeprecation();
    return cjs.SassCalculation;
  },
  get CalculationOperation() {
    defaultExportDeprecation();
    return cjs.CalculationOperation;
  },
  get CalculationInterpolation() {
    defaultExportDeprecation();
    return cjs.CalculationInterpolation;
  },
  get SassColor() {
    defaultExportDeprecation();
    return cjs.SassColor;
  },
  get SassFunction() {
    defaultExportDeprecation();
    return cjs.SassFunction;
  },
  get SassList() {
    defaultExportDeprecation();
    return cjs.SassList;
  },
  get SassMap() {
    defaultExportDeprecation();
    return cjs.SassMap;
  },
  get SassMixin() {
    defaultExportDeprecation();
    return cjs.SassMixin;
  },
  get SassNumber() {
    defaultExportDeprecation();
    return cjs.SassNumber;
  },
  get SassString() {
    defaultExportDeprecation();
    return cjs.SassString;
  },
  get Value() {
    defaultExportDeprecation();
    return cjs.Value;
  },
  get CustomFunction() {
    defaultExportDeprecation();
    return cjs.CustomFunction;
  },
  get ListSeparator() {
    defaultExportDeprecation();
    return cjs.ListSeparator;
  },
  get sassFalse() {
    defaultExportDeprecation();
    return cjs.sassFalse;
  },
  get sassNull() {
    defaultExportDeprecation();
    return cjs.sassNull;
  },
  get sassTrue() {
    defaultExportDeprecation();
    return cjs.sassTrue;
  },
  get Exception() {
    defaultExportDeprecation();
    return cjs.Exception;
  },
  get PromiseOr() {
    defaultExportDeprecation();
    return cjs.PromiseOr;
  },
  get info() {
    defaultExportDeprecation();
    return cjs.info;
  },
  get render() {
    defaultExportDeprecation();
    return cjs.render;
  },
  get renderSync() {
    defaultExportDeprecation();
    return cjs.renderSync;
  },
  get TRUE() {
    defaultExportDeprecation();
    return cjs.TRUE;
  },
  get FALSE() {
    defaultExportDeprecation();
    return cjs.FALSE;
  },
  get NULL() {
    defaultExportDeprecation();
    return cjs.NULL;
  },
  get types() {
    defaultExportDeprecation();
    return cjs.types;
  },
  get NodePackageImporter() {
    defaultExportDeprecation();
    return cjs.NodePackageImporter;
  },
  get deprecations() {
    defaultExportDeprecation();
    return cjs.deprecations;
  },
  get Version() {
    defaultExportDeprecation();
    return cjs.Version;
  },
  get parser_() {
    defaultExportDeprecation();
    return cjs.parser_;
  },
};
